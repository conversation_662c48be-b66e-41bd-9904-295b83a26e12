<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "0CBA09D9-5831-4F46-AFF0-44A153AD256B"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/SessionManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "60"
            endingLineNumber = "60"
            landmarkName = "openSession(_:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384 - 904eefcaf94ace19"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "PhotoUncle.SessionManager.openSession(PhotoUncle.Session) -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/SessionManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60">
               </Location>
               <Location
                  uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384 - 904eefcaf94ace19"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "PhotoUncle.SessionManager.openSession(PhotoUncle.Session) -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/SessionManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60">
               </Location>
               <Location
                  uuid = "4CCD82F5-225F-4FCC-953B-E03A3ED18384 - 426e7d74f34be616"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (PhotoUncle.Session) -&gt; Swift.Bool in PhotoUncle.SessionManager.openSession(PhotoUncle.Session) -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/SessionManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "60"
                  endingLineNumber = "60">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8460A60A-269F-439F-9227-63C953D55404"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "185"
            endingLineNumber = "185"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D590CF05-B7F8-4E88-9EDC-04DFC4907BD3"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/SDImageViewer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "151"
            endingLineNumber = "151"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "320B0E4B-4453-4247-B1B6-D35B735A039E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/SDImageViewer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "128"
            endingLineNumber = "128"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B96611A0-0B19-4615-A93F-3B3DB6F45C3E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/SDImageViewer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "464"
            endingLineNumber = "464"
            landmarkName = "calculateRotatedImageSize()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C3FD47EA-7BFA-4000-8005-1784B2FC5FCD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/SDImageViewer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "112"
            endingLineNumber = "112"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E59D2E6C-F6CD-49E9-A328-11D706E65565"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/SDImageViewer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "385"
            endingLineNumber = "385"
            landmarkName = "unifiedControlPanel"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "9CB05750-CCE8-46D5-9B17-B86D7272F8A7"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "316"
            endingLineNumber = "316"
            landmarkName = "toolbar"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E0DFFB4D-17A9-4983-BA0E-32A15BCCC6FE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PhotoUncle/preview/SDImageViewer.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "550"
            endingLineNumber = "550"
            landmarkName = "syncScaleToCoordinator()"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "E0DFFB4D-17A9-4983-BA0E-32A15BCCC6FE - 687189089399bba2"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "PhotoUncle.SDImageViewer.syncScaleToCoordinator() -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/preview/SDImageViewer.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "550"
                  endingLineNumber = "550">
               </Location>
               <Location
                  uuid = "E0DFFB4D-17A9-4983-BA0E-32A15BCCC6FE - 687189089399bba2"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "PhotoUncle.SDImageViewer.syncScaleToCoordinator() -&gt; ()"
                  moduleName = "PhotoUncle.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/work_space/macos/PhotoUncle/PhotoUncle/preview/SDImageViewer.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "550"
                  endingLineNumber = "550">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
