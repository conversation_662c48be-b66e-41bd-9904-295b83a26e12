<?xml version="1.0" encoding="UTF-8"?>
<VariablesViewState
   version = "1.0">
   <ContextStates>
      <ContextState
         contextName = "closure #1 in closure #4 in closure #2 in closure #2 in closure #1 in SDImageViewer.body.getter:SDImageViewer.swift">
      </ContextState>
      <ContextState
         contextName = "closure #1 in ThumbnailPreloader.preloadThumbnail(for:):OptimizedThumbListView.swift">
      </ContextState>
      <ContextState
         contextName = "closure #2 in closure #2 in closure #1 in SDImageViewer.body.getter:SDImageViewer.swift">
         <PersistentStrings>
            <PersistentString
               value = "offset">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
      <ContextState
         contextName = "closure #1 in SDImageViewer.body.getter:SDImageViewer.swift">
      </ContextState>
      <ContextState
         contextName = "SDImageViewer.calculateSwitchOffset(imageSize:containerSize:fitScale:):SDImageViewer.swift">
         <PersistentStrings>
            <PersistentString
               value = "self.scale">
            </PersistentString>
         </PersistentStrings>
      </ContextState>
   </ContextStates>
</VariablesViewState>
