//
//  ViewerCoordinator.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//

import SwiftUI
import Combine

// MARK: - Viewer State
class ViewerState: ObservableObject {
    @Published var scale: CGFloat = 1.0
    @Published var offset: CGSize = .zero
    @Published var dragOffset: CGSize = .zero
    @Published var rotation: Angle = .degrees(0)
    @Published var isRotationEnabled: Bool = true

    // 计算总偏移量
    var totalOffset: CGSize {
        CGSize(
            width: offset.width + dragOffset.width,
            height: offset.height + dragOffset.height
        )
    }

    init(isRotationEnabled: Bool = true) {
        self.isRotationEnabled = isRotationEnabled
    }

    /// 重置所有状态
    func reset() {
        scale = 1.0
        offset = .zero
        dragOffset = .zero
        rotation = .degrees(0)
    }
}

// MARK: - Viewer Coordinator
class ViewerCoordinator: ObservableObject {
    @Published var viewerState = ViewerState()
    @Published var isMultiColumnMode = false

    init() {
        // 初始化时设置旋转状态
        viewerState.isRotationEnabled = true
    }

    // MARK: - Public Methods

    /// 设置多列模式
    func setMultiColumnMode(_ enabled: Bool) {
        isMultiColumnMode = enabled

        // 在多列模式下禁用旋转
        if enabled {
            viewerState.isRotationEnabled = false
            viewerState.rotation = .degrees(0) // 重置旋转
        } else {
            viewerState.isRotationEnabled = true
        }
    }

    /// 重置所有状态
    func resetState() {
        viewerState.reset()
        viewerState.isRotationEnabled = !isMultiColumnMode
    }
}

// MARK: - Environment Keys
struct ViewerCoordinatorKey: EnvironmentKey {
    static let defaultValue: ViewerCoordinator? = nil
}

struct ViewerStateKey: EnvironmentKey {
    static let defaultValue: ViewerState? = nil
}

extension EnvironmentValues {
    var viewerCoordinator: ViewerCoordinator? {
        get { self[ViewerCoordinatorKey.self] }
        set { self[ViewerCoordinatorKey.self] = newValue }
    }

    var viewerState: ViewerState? {
        get { self[ViewerStateKey.self] }
        set { self[ViewerStateKey.self] = newValue }
    }
}

// MARK: - View Extensions
extension View {
    func viewerCoordinator(_ coordinator: ViewerCoordinator) -> some View {
        environment(\.viewerCoordinator, coordinator)
            .environment(\.viewerState, coordinator.viewerState)
    }

    func viewerState(_ state: ViewerState) -> some View {
        environment(\.viewerState, state)
    }
}
