//
//  ViewerCoordinator.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//

import SwiftUI
import Combine

// MARK: - Viewer State
struct ViewerState {
    var scale: CGFloat = 1.0
    var offset: CGSize = .zero
    var dragOffset: CGSize = .zero
    var rotation: Angle = .degrees(0)
    var isRotationEnabled: Bool = true
    
    // 计算总偏移量
    var totalOffset: CGSize {
        CGSize(
            width: offset.width + dragOffset.width,
            height: offset.height + dragOffset.height
        )
    }
}

// MARK: - Viewer Coordinator
class ViewerCoordinator: ObservableObject {
    @Published var sharedState = ViewerState()
    @Published var isMultiColumnMode = false
    
    private var cancellables = Set<AnyCancellable>()
    private var isUpdating = false // 防止循环更新
    
    // 存储所有注册的viewer更新回调
    private var viewerUpdateCallbacks: [String: (ViewerState) -> Void] = [:]
    
    init() {
        setupStateObservation()
    }
    
    private func setupStateObservation() {
        // 监听状态变化，同步到所有viewer
        $sharedState
            .dropFirst() // 跳过初始值
            .sink { [weak self] newState in
                self?.syncToAllViewers(newState)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 设置多列模式
    func setMultiColumnMode(_ enabled: Bool) {
        isMultiColumnMode = enabled
        
        // 在多列模式下禁用旋转
        if enabled {
            var newState = sharedState
            newState.isRotationEnabled = false
            newState.rotation = .degrees(0) // 重置旋转
            updateSharedState(newState)
        } else {
            var newState = sharedState
            newState.isRotationEnabled = true
            updateSharedState(newState)
        }
    }
    
    /// 注册viewer的更新回调
    func registerViewer(id: String, updateCallback: @escaping (ViewerState) -> Void) {
        viewerUpdateCallbacks[id] = updateCallback
        
        // 立即同步当前状态到新注册的viewer
        updateCallback(sharedState)
    }
    
    /// 注销viewer
    func unregisterViewer(id: String) {
        viewerUpdateCallbacks.removeValue(forKey: id)
    }
    
    /// 从某个viewer更新状态
    func updateFromViewer(id: String, newState: ViewerState) {
        guard !isUpdating else { return }
        
        // 在多列模式下，忽略旋转更新
        var stateToUpdate = newState
        if isMultiColumnMode {
            stateToUpdate.rotation = sharedState.rotation
            stateToUpdate.isRotationEnabled = false
        }
        
        updateSharedState(stateToUpdate)
    }
    
    /// 更新缩放
    func updateScale(_ scale: CGFloat, fromViewer id: String? = nil) {
        var newState = sharedState
        newState.scale = scale
        updateSharedState(newState)
    }
    
    /// 更新偏移
    func updateOffset(_ offset: CGSize, fromViewer id: String? = nil) {
        var newState = sharedState
        newState.offset = offset
        updateSharedState(newState)
    }
    
    /// 更新拖拽偏移
    func updateDragOffset(_ dragOffset: CGSize, fromViewer id: String? = nil) {
        var newState = sharedState
        newState.dragOffset = dragOffset
        updateSharedState(newState)
    }
    
    /// 更新旋转（仅在单列模式下有效）
    func updateRotation(_ rotation: Angle, fromViewer id: String? = nil) {
        guard !isMultiColumnMode else { return }
        
        var newState = sharedState
        newState.rotation = rotation
        updateSharedState(newState)
    }
    
    /// 重置所有状态
    func resetState() {
        let newState = ViewerState(isRotationEnabled: !isMultiColumnMode)
        updateSharedState(newState)
    }
    
    // MARK: - Private Methods
    
    private func updateSharedState(_ newState: ViewerState) {
        guard !isUpdating else { return }
        
        isUpdating = true
        sharedState = newState
        isUpdating = false
    }
    
    private func syncToAllViewers(_ state: ViewerState) {
        guard !isUpdating else { return }
        
        // 同步到所有注册的viewer
        for (_, callback) in viewerUpdateCallbacks {
            callback(state)
        }
    }
}

// MARK: - Viewer Coordinator Environment Key
struct ViewerCoordinatorKey: EnvironmentKey {
    static let defaultValue: ViewerCoordinator? = nil
}

extension EnvironmentValues {
    var viewerCoordinator: ViewerCoordinator? {
        get { self[ViewerCoordinatorKey.self] }
        set { self[ViewerCoordinatorKey.self] = newValue }
    }
}

// MARK: - View Extension
extension View {
    func viewerCoordinator(_ coordinator: ViewerCoordinator) -> some View {
        environment(\.viewerCoordinator, coordinator)
    }
}
