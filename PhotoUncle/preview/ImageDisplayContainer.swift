//
//  ImageDisplayContainer.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//

import SwiftUI

struct ImageDisplayContainer: View {
    let photos: [PhotoItem]
    let selectedPhoto: PhotoItem?
    let displayMode: ImageDisplayMode
    let gridColumns: Int
    let onPhotoSelected: (PhotoItem) -> Void

    // 为单个SDImageViewer创建独立的coordinator
    @StateObject private var singleViewerCoordinator = ViewerCoordinator()
    
    var body: some View {
        Group {
            switch displayMode {
            case .single:
                singleImageView
            case .grid:
                gridImageView
            }
        }
        .animation(.easeInOut(duration: 0.3), value: displayMode)
    }
    
    // MARK: - Single Image View
    @ViewBuilder
    private var singleImageView: some View {
        if let selectedPhoto = selectedPhoto {
            // 单个SDImageViewer也使用coordinator提供的ViewerState
            SDImageViewer(imageURL: selectedPhoto.url)
                .viewerCoordinator(singleViewerCoordinator)
                .onAppear {
                    // 单个模式下启用旋转
                    singleViewerCoordinator.setMultiColumnMode(false)
                }
        } else {
            VStack {
                Image(systemName: "photo")
                    .font(.system(size: 64))
                    .foregroundColor(.secondary)
                Text("选择一张图片查看")
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.black)
        }
    }
    
    // MARK: - Grid Image View
    @ViewBuilder
    private var gridImageView: some View {
        GridImageViewer(
            photos: photos,
            selectedPhoto: selectedPhoto,
            columns: gridColumns,
            onPhotoSelected: onPhotoSelected
        )
    }
}

#Preview {
    if let url = URL(string: "https://picsum.photos/800/600") {
        let samplePhotos = [
            PhotoItem(url: url),
            PhotoItem(url: url),
            PhotoItem(url: url),
            PhotoItem(url: url)
        ]
        
        ImageDisplayContainer(
            photos: samplePhotos,
            selectedPhoto: samplePhotos.first,
            displayMode: .grid,
            gridColumns: 3,
            onPhotoSelected: { _ in }
        )
        .frame(width: 800, height: 600)
    }
}
