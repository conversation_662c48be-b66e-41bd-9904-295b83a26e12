//
//  SDImageViewer.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI
import SDWebImageSwiftUI

struct SDImageViewer: View {
    let imageURL: URL
    let viewerId: String

    @State private var scale: CGFloat = 1.0
    @State private var rotation: Angle = .degrees(0)
    @State private var offset: CGSize = .zero
    @State private var dragOffset: CGSize = .zero
    @State private var imageSize: CGSize = .zero
    @State private var showControls: Bool = true
    @State private var isLoading: Bool = true
    @State private var lastScale: CGFloat = 1.0
    @State private var hideControlsTimer: Timer?
    @State private var isDragging: Bool = false
    @State private var isScaling: Bool = false // 新增：跟踪缩放状态
    @State private var previousImageURL: URL? = nil // 用于跟踪 URL 变化
    @State private var containerSize: CGSize = .zero // 存储容器尺寸
    @AppStorage("dragSpeed") private var dragSpeed: Double = 1.0 // 拖动系数，范围0.1-10
    @AppStorage("switchImagePosition") private var switchImagePosition: String = "middle" // 切换时显示位置：top, middle, bottom
    private let scrollFactor = 5.0
    @State private var fitScale = 1.0
    // 优化缩放范围
    private let minScale: CGFloat = 0.1
    private let maxScale: CGFloat = 50.0

    // ViewerState相关
    @Environment(\.viewerState) private var sharedViewerState
    @Environment(\.viewerCoordinator) private var coordinator
    @State private var isSharedMode = false

    // 初始化方法
    init(imageURL: URL, viewerId: String = UUID().uuidString) {
        self.imageURL = imageURL
        self.viewerId = viewerId
    }

    // 计算属性：是否允许旋转
    private var isRotationEnabled: Bool {
        sharedViewerState?.isRotationEnabled ?? true
    }

    var body: some View {
        GeometryReader { geometry in
            let containerSize = geometry.size

            // 将容器尺寸存储到状态中，供其他地方使用
            let _ = DispatchQueue.main.async {
                if containerSize.width > 0 && containerSize.height > 0 {
                    self.containerSize = containerSize
                }
            }

            // 决定使用本地状态还是共享状态
            let currentScale = sharedViewerState?.scale ?? scale
            let currentOffset = sharedViewerState?.offset ?? offset
            let currentDragOffset = sharedViewerState?.dragOffset ?? dragOffset
            let currentRotation = sharedViewerState?.rotation ?? rotation

            ZStack {
                // 渐变背景
                LinearGradient(
                    colors: [Color.black, Color.gray.opacity(0.3), Color.black],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )

                if isLoading {
                    // 加载指示器
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.5)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        Text("加载中...")
                            .foregroundColor(.white.opacity(0.8))
                            .font(.system(size: 14, weight: .medium))
                    }
                }

                ZStack {
                    // 手势处理层 - 放在最底层，覆盖整个区域
                    GestureHandler(
                        onDragChanged: { translation in
                            print("🎯 拖拽中: \(translation)")
                            isDragging = true
                            // 更新状态
                            if let sharedState = sharedViewerState {
                                sharedState.dragOffset = translation
                            } else {
                                dragOffset = translation
                            }
                        },
                        onDragEnded: { translation in
                            print("🎯 拖拽结束: \(translation)")
                            isDragging = false
                            // 更新状态
                            if let sharedState = sharedViewerState {
                                sharedState.offset.width += translation.width
                                sharedState.offset.height += translation.height
                                sharedState.dragOffset = .zero
                            } else {
                                offset.width += translation.width
                                offset.height += translation.height
                                dragOffset = .zero
                            }
                        },
                        onScrollWheel: { deltaY, isCmdPressed in
                            print("🎯 滚轮事件: deltaY = \(deltaY), Cmd = \(isCmdPressed)")
                            // 防止在拖动或缩放时触发滚轮事件
                            guard !isDragging && !isScaling else { return }
                            
                            if isCmdPressed {
                                // Cmd + 滚轮 = 上下拖动
                                offset.height -= deltaY * scale * 2 * CGFloat(dragSpeed) // 应用拖动系数
                                
                            } else {
                                // 普通滚轮 = 缩放
                                let zoomFactor: CGFloat = 1.1
                                let newScale: CGFloat
                                if deltaY > 0 {
                                    newScale = min(maxScale, scale * zoomFactor)
                                } else {
                                    newScale = max(minScale, scale / zoomFactor)
                                }
                                
                                // 添加缩放阈值，避免微小缩放
                                let scaleThreshold: CGFloat = 0.01
                                let currentScaleValue = sharedViewerState?.scale ?? scale
                                if abs(newScale - currentScaleValue) > scaleThreshold {
                                    if let sharedState = sharedViewerState {
                                        sharedState.scale = newScale
                                    } else {
                                        scale = newScale
                                    }
                                }
                            }
                        }
                    )
                    .frame(maxWidth: .infinity, maxHeight: .infinity)

                    // 图片显示层
                    WebImage(url: imageURL)
                        .onSuccess { image, data, cacheType in
                            // 获取图片尺寸
                            print("🖼️ 图片加载成功: \(image.size)")
                            
                            
                            // 根据设置决定是否重置 offset
                            DispatchQueue.main.async{
                                imageSize = image.size
                                isLoading = false
                                print("🔄 切换位置设置: \(switchImagePosition)")
                                print("🔄 当前容器尺寸: \(self.containerSize)")
                                
                                
                                // 根据切换位置设置计算垂直偏移
                                let currentContainerSize = self.containerSize.width > 0 ? self.containerSize : containerSize
                                print("🔄 使用的容器尺寸: \(currentContainerSize)")
                                fitScale = fitScaleFor(imageSize: imageSize, containerSize: currentContainerSize)
                                let switchOffset = calculateSwitchOffset(imageSize: imageSize, containerSize: currentContainerSize)
                                offset.height = switchOffset
                                print("🔄 应用切换位置偏移: \(switchOffset), 位置: \(switchImagePosition)")
                            }
                        }
                        .onFailure { error in
                            print("❌ 图片加载失败: \(error)")
                            isLoading = false
                        }
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .scaleEffect(currentScale)
                        .rotationEffect(currentRotation, anchor: .center)
                        .offset(x: currentOffset.width + currentDragOffset.width,
                                y: currentOffset.height + currentDragOffset.height)
                        .animation(.none, value: dragOffset) // 拖动时不使用动画，避免抖动
                        
                        .allowsHitTesting(false) // 禁用图片的点击事件，让手势处理层处理
                    
                }
                    .onAppear {
                        print("🔄 SDImageViewer appeared")
                        print("📋 当前设置 - dragSpeed: \(dragSpeed), switchPosition: \(switchImagePosition)")
                        // 初始化 previousImageURL
                        if previousImageURL == nil {
                            previousImageURL = imageURL
                        }

                        // 检查是否为共享模式
                        isSharedMode = sharedViewerState != nil
                    }
                    .onChange(of: imageURL) { newURL in
                        print("🔄 imageURL 发生变化: \(newURL)")
                        print("🔄 切换位置设置: \(switchImagePosition)")
                        
                        // 重置图片尺寸，等待新图片加载
                        imageSize = .zero
                        isLoading = true
                        offset.width = 0
                        offset.height = 0
                        dragOffset.width = 0
                        dragOffset.height = 0
                        previousImageURL = newURL
                        
                    }
                    .onDisappear {
                        print("🔄 SDImageViewer disappeared")
                    }
                    .simultaneousGesture(
                        // 缩放手势 - 使用simultaneousGesture与拖拽共存
                        MagnificationGesture()
                            .onChanged { value in
                                print("🔍 缩放手势: value = \(value), lastScale = \(lastScale)")
                                // 防止在拖动时触发缩放
                                guard !isDragging else { return }
                                
                                isScaling = true
                                let delta = value / lastScale
                                let newScale = min(maxScale, max(minScale, scale * delta))
                                
                                // 添加缩放阈值，避免微小缩放
                                let scaleThreshold: CGFloat = 0.01
                                let currentScaleValue = sharedViewerState?.scale ?? scale
                                if abs(newScale - currentScaleValue) > scaleThreshold {
                                    if let sharedState = sharedViewerState {
                                        sharedState.scale = newScale
                                    } else {
                                        scale = newScale
                                    }
                                    lastScale = value
                                    print("🔍 新scale = \(newScale)")
                                }
                            }
                            .onEnded { _ in
                                print("🔍 缩放手势结束")
                                lastScale = 1.0
                                isScaling = false
                            }
                    )
                    .onTapGesture(count: 1) {
                        // 单击显示/隐藏控制栏
                        print("👆 单击手势")
                        if !isDragging {
                            print("👆 切换控制栏显示")
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showControls.toggle()
                            }
                        }
                    }
                    .onKeyPress(.downArrow) {
                        // 键盘上箭头 = 向上拖动
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            offset.height -= 50 * CGFloat(dragSpeed)
                            
                        }
                        return .handled
                    }
                    .onKeyPress(.upArrow) {
                        // 键盘下箭头 = 向下拖动
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            offset.height += 50 * CGFloat(dragSpeed)
                            
                        }
                        return .handled
                    }
                    .onHover { hovering in
                        print("🖱️ hover: \(hovering)")
                        // 取消之前的隐藏定时器
                        hideControlsTimer?.invalidate()
                        
                        if hovering {
                            // 鼠标进入时立即显示控制栏
                            print("🖱️ 显示控制栏")
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showControls = true
                            }
                        } else {
                            // 鼠标离开时延迟隐藏控制栏，避免在控制面板上操作时闪烁
                            print("🖱️ 延迟隐藏控制栏")
                            hideControlsTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { _ in
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showControls = false
                                }
                            }
                        }
                    }

                // 控制面板 - 只在显示时可见
                if showControls {
                    // 底部统一控制面板
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            unifiedControlPanel
                                .padding(.horizontal, 20)
                                .padding(.bottom, 20)
                                .onHover { hovering in
                                    // 当鼠标悬停在控制面板上时，取消隐藏定时器并保持显示状态
                                    hideControlsTimer?.invalidate()
                                    if hovering {
                                        showControls = true
                                    }
                                }
                        }
                    }
                }
            }
        }
        .clipped()
        .background(Color.black)
        .onDisappear {
            // 清理定时器
            hideControlsTimer?.invalidate()
            hideControlsTimer = nil
        }
    }



    var unifiedControlPanel: some View {
        ZStack {
            // 透明背景 - 不阻挡手势
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
                .allowsHitTesting(false) // 背景不阻挡手势
            
            // 统一控制内容 - 可以接收点击
            HStack(spacing: 20) {
                // 左侧：旋转和重置按钮
                HStack(spacing: 12) {
                    // 旋转控制（仅在允许旋转时显示）
                    if isRotationEnabled {
                        ControlButton(
                            icon: "rotate.left",
                            action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    if let sharedState = sharedViewerState {
                                        sharedState.rotation += .degrees(-90)
                                    } else {
                                        rotation += .degrees(-90)
                                    }
                                    // 旋转后调整偏移量，确保图片在视图内
                                    adjustOffsetAfterRotation()
                                }
                            }
                        )

                        ControlButton(
                            icon: "rotate.right",
                            action: {
                                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                    if let sharedState = sharedViewerState {
                                        sharedState.rotation += .degrees(90)
                                    } else {
                                        rotation += .degrees(90)
                                    }
                                    // 旋转后调整偏移量，确保图片在视图内
                                    adjustOffsetAfterRotation()
                                }
                            }
                        )
                    }

                    Divider()
                        .frame(height: 24)
                        .background(Color.white.opacity(0.3))

                    // 重置按钮
                    ControlButton(
                        icon: "arrow.counterclockwise",
                        action: {
                            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                                if let sharedState = sharedViewerState {
                                    sharedState.reset()
                                } else {
                                    scale = 1.0
                                    rotation = .zero
                                    offset = .zero
                                    dragOffset = .zero
                                }
                                // 重置后重新计算适配缩放比例
                                if imageSize.width > 0 && imageSize.height > 0 && containerSize.width > 0 && containerSize.height > 0 {
                                    fitScale = fitScaleFor(imageSize: imageSize, containerSize: containerSize)
                                }
                            }
                        }
                    )
                }

                Divider()
                    .frame(height: 40)
                    .background(Color.white.opacity(0.3))

                // 中间：缩放控制
                HStack(spacing: 12) {
                    ControlButton(
                        icon: "minus.magnifyingglass",
                        action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                let currentScaleValue = sharedViewerState?.scale ?? scale
                                let newScale = max(minScale, currentScaleValue / 1.2)
                                if let sharedState = sharedViewerState {
                                    sharedState.scale = newScale
                                } else {
                                    scale = newScale
                                }
                            }
                        }
                    )
                    
                    // 缩放百分比显示
                    Text("\(Int(scale * 100))%")
                        .foregroundColor(.white)
                        .font(.system(size: 14, weight: .semibold))
                        .monospacedDigit()
                        .frame(width: 60)
                    
                    ControlButton(
                        icon: "plus.magnifyingglass",
                        action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                let currentScaleValue = sharedViewerState?.scale ?? scale
                                let newScale = min(maxScale, currentScaleValue * 1.2)
                                if let sharedState = sharedViewerState {
                                    sharedState.scale = newScale
                                } else {
                                    scale = newScale
                                }
                            }
                        }
                    )
                }

                Divider()
                    .frame(height: 40)
                    .background(Color.white.opacity(0.3))

                // 右侧：缩放滑块
                HStack(spacing: 12) {
                    Slider(value: $scale, in: minScale...maxScale) {
                        Text("缩放").foregroundColor(.white).font(.system(size: 13, weight: .medium))
                    }
                    .accentColor(.white)
                    .frame(width: 120)
                }
            }
            .padding(16)
        }
        .frame(height: 72) // 恢复原来的高度
        .frame(maxWidth: 600) // 限制最大宽度
    }



    /// 计算切换图片时的垂直偏移量
        func calculateSwitchOffset(imageSize: CGSize, containerSize: CGSize) -> CGFloat {
            
            let swiftUIFitScale = fitScale
            let scaledImageHeight = imageSize.height * swiftUIFitScale * scale // ✅ 加上 scale
            let containerHeight = containerSize.height

            if scaledImageHeight <= containerHeight {
                return 0
            }

            switch switchImagePosition {
            case "top":
                return (scaledImageHeight - containerHeight) / 2
            case "bottom":
                return -(scaledImageHeight - containerHeight) / 2
            case "middle":
                return 0
            default:
                return 0
            }
        }
    
    /// 优化初始图片显示尺寸计算
    func fitScaleFor(imageSize: CGSize, containerSize: CGSize) -> CGFloat {
        guard imageSize.width > 0 && imageSize.height > 0 &&
              containerSize.width > 0 && containerSize.height > 0 else { return 1.0 }

        // 计算适配缩放比例
        let scaleToFitWidth = containerSize.width / imageSize.width
        let scaleToFitHeight = containerSize.height / imageSize.height

        // 选择较小的缩放比例以确保图片完全显示在容器内
        let fitScale = min(scaleToFitWidth, scaleToFitHeight)
            return fitScale
    }
    
    /// 旋转后调整偏移量，确保图片在视图内可见
    private func adjustOffsetAfterRotation() {
        guard imageSize.width > 0 && imageSize.height > 0 && containerSize.width > 0 && containerSize.height > 0 else {
            return
        }
        
        // 计算旋转后的图片边界
        let rotatedImageSize = calculateRotatedImageSize()
        let scaledRotatedSize = CGSize(
            width: rotatedImageSize.width * fitScale * scale,
            height: rotatedImageSize.height * fitScale * scale
        )
        
        // 计算需要调整的偏移量
        let maxOffsetX = max(0, (scaledRotatedSize.width - containerSize.width) / 2)
        let maxOffsetY = max(0, (scaledRotatedSize.height - containerSize.height) / 2)
        
        // 限制偏移量在合理范围内
        offset.width = max(-maxOffsetX, min(maxOffsetX, offset.width))
        offset.height = max(-maxOffsetY, min(maxOffsetY, offset.height))
        
        print("🔄 旋转后调整偏移量: 图片尺寸=\(rotatedImageSize), 缩放后=\(scaledRotatedSize), 新偏移=\(offset)")
    }
    
    /// 计算旋转后的图片尺寸
    private func calculateRotatedImageSize() -> CGSize {
        let radians = rotation.radians
        let cosValue = abs(cos(radians))
        let sinValue = abs(sin(radians))
        
        // 计算旋转后的宽度和高度
        let rotatedWidth = imageSize.width * cosValue + imageSize.height * sinValue
        let rotatedHeight = imageSize.width * sinValue + imageSize.height * cosValue
        
        return CGSize(width: rotatedWidth, height: rotatedHeight)
    }

}

// 手势处理器组件
struct GestureHandler: NSViewRepresentable {
    let onDragChanged: (CGSize) -> Void
    let onDragEnded: (CGSize) -> Void
    let onScrollWheel: (CGFloat, Bool) -> Void
    
    func makeNSView(context: Context) -> NSView {
        print("🔧 创建GestureHandler NSView")
        let view = GestureView()
        view.onDragChanged = onDragChanged
        view.onDragEnded = onDragEnded
        view.onScrollWheel = onScrollWheel
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        print("🔧 更新GestureHandler NSView (1)")
    }
    
    class GestureView: NSView {
        var onDragChanged: ((CGSize) -> Void)?
        var onDragEnded: ((CGSize) -> Void)?
        var onScrollWheel: ((CGFloat, Bool) -> Void)?
        private var initialDragLocation: NSPoint?
        private var totalTranslation: CGSize = .zero
        private var lastDragUpdate: Date = Date()
        private let dragUpdateInterval: TimeInterval = 1.0 / 60.0 // 限制到60fps

        override init(frame frameRect: NSRect) {
            super.init(frame: frameRect)
            setupView()
        }

        required init?(coder: NSCoder) {
            super.init(coder: coder)
            setupView()
        }

        private func setupView() {
            // 确保视图可以接收鼠标事件
            wantsLayer = true
            layer?.backgroundColor = NSColor.clear.cgColor
        }

        override func mouseDown(with event: NSEvent) {
            print("🖱️ mouseDown: \(event.locationInWindow)")
            // 转换为视图坐标
            let locationInView = convert(event.locationInWindow, from: nil)
            initialDragLocation = locationInView
            totalTranslation = .zero
            lastDragUpdate = Date()
            window?.makeFirstResponder(self)
        }

        override func mouseDragged(with event: NSEvent) {
            // 限制更新频率，避免过度更新导致抖动
            let now = Date()
            guard now.timeIntervalSince(lastDragUpdate) >= dragUpdateInterval else {
                return
            }
            lastDragUpdate = now
            
            print("🖱️ mouseDragged: \(event.locationInWindow)")
            guard let initialLocation = initialDragLocation else {
                print("❌ 没有初始位置")
                return
            }
            // 转换为视图坐标
            let currentLocation = convert(event.locationInWindow, from: nil)

            // 计算偏移量，使用更精确的坐标系转换
            let deltaX = currentLocation.x - initialLocation.x
            let deltaY = -(currentLocation.y - initialLocation.y) // 翻转 Y 轴
            
            // 添加阈值，避免微小移动
            let threshold: CGFloat = 0.5
            if abs(deltaX) < threshold && abs(deltaY) < threshold {
                return
            }
            
            totalTranslation = CGSize(width: deltaX, height: deltaY)
            print("📏 计算偏移: \(totalTranslation)")
            onDragChanged?(totalTranslation)
        }

        override func mouseUp(with event: NSEvent) {
            print("🖱️ mouseUp: \(event.locationInWindow)")
            guard let initialLocation = initialDragLocation else {
                print("❌ 没有初始位置")
                return
            }
            // 转换为视图坐标
            let currentLocation = convert(event.locationInWindow, from: nil)

            // 计算最终偏移量，使用更精确的坐标系转换
            let finalTranslation = CGSize(
                width: currentLocation.x - initialLocation.x,
                height: -(currentLocation.y - initialLocation.y) // 翻转 Y 轴
            )
            print("📏 最终偏移: \(finalTranslation)")
            onDragEnded?(finalTranslation)
            initialDragLocation = nil
            totalTranslation = .zero
        }

        override func scrollWheel(with event: NSEvent) {
            print("🖱️ scrollWheel: deltaY = \(event.scrollingDeltaY), deltaX = \(event.scrollingDeltaX)")
            // 处理滚轮事件，优先处理垂直滚动
            let deltaY = event.scrollingDeltaY
            let isCmdPressed = event.modifierFlags.contains(.command)
            
            if abs(deltaY) > 0.1 { // 添加阈值避免微小滚动
                onScrollWheel?(deltaY, isCmdPressed)
            }
        }

        override var acceptsFirstResponder: Bool {
            print("🔧 acceptsFirstResponder: true")
            return true
        }

        override func becomeFirstResponder() -> Bool {
            print("🔧 becomeFirstResponder: true")
            return true
        }

        override func updateTrackingAreas() {
            super.updateTrackingAreas()

            // 移除旧的跟踪区域
            for trackingArea in trackingAreas {
                removeTrackingArea(trackingArea)
            }

            // 添加新的跟踪区域以接收鼠标事件
            let trackingArea = NSTrackingArea(
                rect: bounds,
                options: [.activeInKeyWindow, .mouseEnteredAndExited, .mouseMoved],
                owner: self,
                userInfo: nil
            )
            addTrackingArea(trackingArea)
        }
    }
}

// 控制按钮组件
struct ControlButton: View {
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .frame(width: 36, height: 36)
                .background(
                    Circle()
                        .fill(.ultraThinMaterial)
                        .shadow(color: .black.opacity(0.2), radius: 4, x: 0, y: 2)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(1.0)
        .animation(.spring(response: 0.2, dampingFraction: 0.8), value: true)
        .allowsHitTesting(true) // 确保按钮可以接收点击事件
    }
}



// 扩展View以支持滚轮事件（保留以备后用）
extension View {
    func onScrollWheel(perform action: @escaping (NSEvent) -> Void) -> some View {
        self.background(ScrollWheelHandler(onScrollWheel: action))
    }
}

struct ScrollWheelHandler: NSViewRepresentable {
    let onScrollWheel: (NSEvent) -> Void

    func makeNSView(context: Context) -> NSView {
        let view = ScrollWheelView()
        view.onScrollWheel = onScrollWheel
        return view
    }

    func updateNSView(_ nsView: NSView, context: Context) {
        print("🔧 更新ScrollWheelHandler NSView (2)")
    }

    class ScrollWheelView: NSView {
        var onScrollWheel: ((NSEvent) -> Void)?

        override func scrollWheel(with event: NSEvent) {
            onScrollWheel?(event)
        }

        override var acceptsFirstResponder: Bool {
            return true
        }
        
        override func becomeFirstResponder() -> Bool {
            return true
        }
    }
}

#Preview {
    if let url = URL(string: "https://picsum.photos/800/600") {
        SDImageViewer(imageURL: url)
            .frame(width: 800, height: 600)
    }
}
