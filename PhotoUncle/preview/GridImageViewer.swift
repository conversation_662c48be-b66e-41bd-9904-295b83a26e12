//
//  GridImageViewer.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//

import SwiftUI
import SDWebImageSwiftUI

struct GridImageViewer: View {
    let photos: [PhotoItem]
    let selectedPhoto: PhotoItem?
    let columns: Int
    let onPhotoSelected: (PhotoItem) -> Void

    @State private var containerSize: CGSize = .zero
    @StateObject private var coordinator = ViewerCoordinator()

    // 计算当前选中图片的索引
    private var selectedIndex: Int {
        guard let selectedPhoto = selectedPhoto,
              let index = photos.firstIndex(of: selectedPhoto) else {
            return 0
        }
        return index
    }

    // 计算每列应该显示的图片
    private func getPhotosForColumn(_ columnIndex: Int) -> [PhotoItem] {
        // 从当前选中的图片开始，计算每列的起始索引
        let baseIndex = selectedIndex - (selectedIndex % columns)
        let startIndex = baseIndex + columnIndex

        guard startIndex < photos.count else { return [] }
        return [photos[startIndex]]
    }
    
    var body: some View {
        GeometryReader { geometry in
            let containerSize = geometry.size

            // 将容器尺寸存储到状态中
            let _ = DispatchQueue.main.async {
                if containerSize.width > 0 && containerSize.height > 0 {
                    self.containerSize = containerSize
                }
            }

            if photos.isEmpty {
                // 空状态
                VStack(spacing: 20) {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)

                    Text("没有图片可显示")
                        .font(.title2)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
            } else {
                // 多列SDImageViewer布局
                HStack(spacing: 0) {
                    ForEach(0..<columns, id: \.self) { columnIndex in
                        let columnPhotos = getPhotosForColumn(columnIndex)

                        if !columnPhotos.isEmpty, let photo = columnPhotos.first {
                            SDImageViewer(
                                imageURL: photo.url,
                                viewerId: "column_\(columnIndex)_\(photo.id)"
                            )
                            .frame(width: containerSize.width / CGFloat(columns))
                            .viewerState(coordinator.viewerState)
                            .onTapGesture {
                                onPhotoSelected(photo)
                            }
                        } else {
                            // 空列占位
                            Rectangle()
                                .fill(Color.black)
                                .frame(width: containerSize.width / CGFloat(columns))
                        }
                    }
                }
            }
        }
        .clipped()
        .background(Color.black)
        .viewerCoordinator(coordinator)
        .onAppear {
            // 启用多列模式
            coordinator.setMultiColumnMode(true)
        }
        .onDisappear {
            // 禁用多列模式
            coordinator.setMultiColumnMode(false)
        }
    }
}

#Preview {
    if let url = URL(string: "https://picsum.photos/800/600") {
        let samplePhotos = [
            PhotoItem(url: url),
            PhotoItem(url: url),
            PhotoItem(url: url),
            PhotoItem(url: url)
        ]
        
        GridImageViewer(
            photos: samplePhotos,
            selectedPhoto: samplePhotos.first,
            columns: 3,
            onPhotoSelected: { _ in }
        )
        .frame(width: 800, height: 600)
    }
}
