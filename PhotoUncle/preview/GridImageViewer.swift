//
//  GridImageViewer.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//

import SwiftUI
import SDWebImageSwiftUI

struct GridImageViewer: View {
    let photos: [PhotoItem]
    let selectedPhoto: PhotoItem?
    let columns: Int
    let onPhotoSelected: (PhotoItem) -> Void
    
    @State private var scrollPosition: CGFloat = 0
    @State private var containerSize: CGSize = .zero
    
    private var gridColumns: [GridItem] {
        Array(repeating: GridItem(.flexible(), spacing: 8), count: columns)
    }
    
    var body: some View {
        GeometryReader { geometry in
            let containerSize = geometry.size
            
            // 将容器尺寸存储到状态中
            let _ = DispatchQueue.main.async {
                if containerSize.width > 0 && containerSize.height > 0 {
                    self.containerSize = containerSize
                }
            }
            
            ZStack {
                // 渐变背景
                LinearGradient(
                    colors: [Color.black, Color.gray.opacity(0.3), Color.black],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                
                if photos.isEmpty {
                    // 空状态
                    VStack(spacing: 20) {
                        Image(systemName: "photo.on.rectangle.angled")
                            .font(.system(size: 64))
                            .foregroundColor(.secondary)
                        
                        Text("没有图片可显示")
                            .font(.title2)
                            .foregroundColor(.secondary)
                    }
                } else {
                    // 网格视图
                    ScrollView(.vertical, showsIndicators: true) {
                        LazyVGrid(columns: gridColumns, spacing: 8) {
                            ForEach(photos, id: \.id) { photo in
                                GridImageCell(
                                    photo: photo,
                                    isSelected: photo == selectedPhoto,
                                    containerWidth: containerSize.width,
                                    columns: columns,
                                    onTap: {
                                        onPhotoSelected(photo)
                                    }
                                )
                            }
                        }
                        .padding(16)
                    }
                    .background(GridScrollHandler { deltaY in
                        // 处理滚轮事件
                        let scrollAmount = deltaY * 3.0 // 调整滚动速度
                        scrollPosition += scrollAmount
                    })
                }
            }
        }
        .clipped()
        .background(Color.black)
    }
}

// MARK: - Grid Image Cell
struct GridImageCell: View {
    let photo: PhotoItem
    let isSelected: Bool
    let containerWidth: CGFloat
    let columns: Int
    let onTap: () -> Void
    
    private var cellSize: CGFloat {
        let spacing: CGFloat = 8
        let padding: CGFloat = 32 // 16 * 2
        let availableWidth = containerWidth - padding - (spacing * CGFloat(columns - 1))
        return max(100, availableWidth / CGFloat(columns))
    }
    
    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.gray.opacity(0.2))
            
            // 图片
            WebImage(url: photo.url)
                .onSuccess { image, data, cacheType in
                    // 图片加载成功
                }
                .onFailure { error in
                    print("❌ 网格图片加载失败: \(error)")
                }
                .resizable()
                .aspectRatio(contentMode: .fill)
                .clipped()
                .cornerRadius(8)
            
            // 选中状态覆盖层
            if isSelected {
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.blue, lineWidth: 3)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.blue.opacity(0.2))
                    )
            }
        }
        .frame(width: cellSize, height: cellSize)
        .onTapGesture {
            onTap()
        }
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Grid Scroll Handler
struct GridScrollHandler: NSViewRepresentable {
    let onScrollWheel: (CGFloat) -> Void
    
    func makeNSView(context: Context) -> NSView {
        let view = GridScrollView()
        view.onScrollWheel = onScrollWheel
        return view
    }
    
    func updateNSView(_ nsView: NSView, context: Context) {
        // 更新视图
    }
    
    class GridScrollView: NSView {
        var onScrollWheel: ((CGFloat) -> Void)?
        
        override init(frame frameRect: NSRect) {
            super.init(frame: frameRect)
            setupView()
        }
        
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            setupView()
        }
        
        private func setupView() {
            wantsLayer = true
            layer?.backgroundColor = NSColor.clear.cgColor
        }
        
        override func scrollWheel(with event: NSEvent) {
            let deltaY = event.scrollingDeltaY
            if abs(deltaY) > 0.1 {
                onScrollWheel?(deltaY)
            }
        }
        
        override var acceptsFirstResponder: Bool {
            return true
        }
        
        override func becomeFirstResponder() -> Bool {
            return true
        }
    }
}

#Preview {
    if let url = URL(string: "https://picsum.photos/800/600") {
        let samplePhotos = [
            PhotoItem(url: url),
            PhotoItem(url: url),
            PhotoItem(url: url),
            PhotoItem(url: url)
        ]
        
        GridImageViewer(
            photos: samplePhotos,
            selectedPhoto: samplePhotos.first,
            columns: 3,
            onPhotoSelected: { _ in }
        )
        .frame(width: 800, height: 600)
    }
}
