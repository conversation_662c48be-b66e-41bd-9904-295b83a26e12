//
//  ThumbListViewModel.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import Foundation
import SwiftUI

// MARK: - ThumbListViewModel
class ThumbListViewModel: ObservableObject {
    @Published var selectedPhoto: PhotoItem?
    @Published var thumbnailPosition: ThumbnailPosition = .bottom
    @Published var isThumbnailCollapsed = false
    @Published var imageDisplayMode: ImageDisplayMode = .single
    @Published var gridColumns: Int = 3

    private let photoLoader: PhotoLoader

    init(photoLoader: PhotoLoader) {
        self.photoLoader = photoLoader
    }

    var photos: [PhotoItem] {
        return photoLoader.photos
    }

    func selectPhoto(_ photo: PhotoItem) {
        selectedPhoto = photo
    }

    func selectNextPhoto() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto) else { return }

        let nextIndex: Int
        if imageDisplayMode == .grid {
            // 网格模式：按列数跳转
            nextIndex = min(currentIndex + gridColumns, photos.count - 1)
        } else {
            // 单图模式：跳转到下一张
            nextIndex = min(currentIndex + 1, photos.count - 1)
        }

        if nextIndex != currentIndex {
            selectedPhoto = photos[nextIndex]
        }
    }

    func selectPreviousPhoto() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto) else { return }

        let previousIndex: Int
        if imageDisplayMode == .grid {
            // 网格模式：按列数跳转
            previousIndex = max(currentIndex - gridColumns, 0)
        } else {
            // 单图模式：跳转到上一张
            previousIndex = max(currentIndex - 1, 0)
        }

        if previousIndex != currentIndex {
            selectedPhoto = photos[previousIndex]
        }
    }

    func getCurrentPhotoIndex() -> Int? {
        guard let currentPhoto = selectedPhoto else { return nil }
        return photos.firstIndex(of: currentPhoto)
    }

    func getPhotoCount() -> Int {
        return photos.count
    }

    func toggleThumbnailCollapse() {
        isThumbnailCollapsed.toggle()
    }

    func setThumbnailPosition(_ position: ThumbnailPosition) {
        thumbnailPosition = position
    }

    func setImageDisplayMode(_ mode: ImageDisplayMode) {
        imageDisplayMode = mode
    }

    func setGridColumns(_ columns: Int) {
        gridColumns = max(1, min(10, columns)) // 限制在1-10列之间
    }

    // 网格模式下的左右导航
    func selectNextColumn() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto),
              imageDisplayMode == .grid else { return }

        let nextIndex = min(currentIndex + 1, photos.count - 1)
        if nextIndex != currentIndex {
            selectedPhoto = photos[nextIndex]
        }
    }

    func selectPreviousColumn() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto),
              imageDisplayMode == .grid else { return }

        let previousIndex = max(currentIndex - 1, 0)
        if previousIndex != currentIndex {
            selectedPhoto = photos[previousIndex]
        }
    }

    // 当照片列表更新时，自动选择第一张照片
    func updateSelection() {
        if selectedPhoto == nil && !photos.isEmpty {
            selectedPhoto = photos[0]
        } else if let selected = selectedPhoto, !photos.contains(selected) {
            // 如果当前选中的照片不在列表中，选择第一张
            selectedPhoto = photos.isEmpty ? nil : photos[0]
        }
    }
}