//
//  ThumbListViewModel.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import Foundation
import SwiftUI

// MARK: - ThumbListViewModel
class ThumbListViewModel: ObservableObject {
    @Published var selectedPhoto: PhotoItem?
    @Published var thumbnailPosition: ThumbnailPosition = .bottom
    @Published var isThumbnailCollapsed = false
    @Published var imageDisplayMode: ImageDisplayMode = .single
    @Published var gridColumns: Int = 3

    private let photoLoader: PhotoLoader

    init(photoLoader: PhotoLoader) {
        self.photoLoader = photoLoader
    }

    var photos: [PhotoItem] {
        return photoLoader.photos
    }

    func selectPhoto(_ photo: PhotoItem) {
        selectedPhoto = photo
    }

    func selectNextPhoto() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto),
              currentIndex < photos.count - 1 else { return }

        selectedPhoto = photos[currentIndex + 1]
    }

    func selectPreviousPhoto() {
        guard let currentPhoto = selectedPhoto,
              let currentIndex = photos.firstIndex(of: currentPhoto),
              currentIndex > 0 else { return }

        selectedPhoto = photos[currentIndex - 1]
    }

    func getCurrentPhotoIndex() -> Int? {
        guard let currentPhoto = selectedPhoto else { return nil }
        return photos.firstIndex(of: currentPhoto)
    }

    func getPhotoCount() -> Int {
        return photos.count
    }

    func toggleThumbnailCollapse() {
        isThumbnailCollapsed.toggle()
    }

    func setThumbnailPosition(_ position: ThumbnailPosition) {
        thumbnailPosition = position
    }

    func setImageDisplayMode(_ mode: ImageDisplayMode) {
        imageDisplayMode = mode
    }

    func setGridColumns(_ columns: Int) {
        gridColumns = max(1, min(10, columns)) // 限制在1-10列之间
    }

    // 当照片列表更新时，自动选择第一张照片
    func updateSelection() {
        if selectedPhoto == nil && !photos.isEmpty {
            selectedPhoto = photos[0]
        } else if let selected = selectedPhoto, !photos.contains(selected) {
            // 如果当前选中的照片不在列表中，选择第一张
            selectedPhoto = photos.isEmpty ? nil : photos[0]
        }
    }
}