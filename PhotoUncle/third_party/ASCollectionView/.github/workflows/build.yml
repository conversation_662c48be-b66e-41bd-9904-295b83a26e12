name: Build

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]
    
env:
  DEVELOPER_DIR: /Applications/Xcode_12.4.app/Contents/Developer

jobs:
  build:

    runs-on: macos-latest

    steps:
    - uses: actions/checkout@v2
    - name: Build
      run: set -o pipefail; xcodebuild -project Demo/ASCollectionViewDemo.xcodeproj -scheme ASCollectionViewDemo -destination platform\=iOS\ Simulator,OS\=14.4,name\=iPhone\ 11 build | xcpretty
    - name: Lint podspec
      run: pod lib lint
