//
//  SettingsManager.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI
import AppKit

class SettingsManager: NSObject, ObservableObject {
    static let shared = SettingsManager()


    override private init() {}
    func openSettings() {
            SettingsWindowController.shared.showWindow()
        }
        
        func settingsWindowClosed() {
            // 执行任何关闭时需要的清理操作
            print("设置窗口已关闭")
        }
 
}

