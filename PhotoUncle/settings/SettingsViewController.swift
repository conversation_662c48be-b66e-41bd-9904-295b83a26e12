//
//  SettingsViewController.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/13.
//
import SwiftUI
import AppKit
class SettingsWindowController: NSWindowController, NSWindowDelegate {
    static let shared = SettingsWindowController()
    
    init() {
        let settingsView = SettingsWindow()
        let hostingController = NSHostingController(rootView: settingsView)
        
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 450, height: 500),
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        window.title = "PhotoUncle 设置"
        window.contentViewController = hostingController
       
        
        super.init(window: window)
        window.delegate = self
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    
    
    func showWindow() {
        window?.center()
        window?.makeKeyAndOrderFront(nil)
    }
    
    // MARK: - NSWindowDelegate
    
    func windowWillClose(_ notification: Notification) {
        // 处理窗口关闭时的清理工作
        SettingsManager.shared.settingsWindowClosed()
    }
    
    func windowShouldClose(_ sender: NSWindow) -> Bool {
        return true
    }
}
